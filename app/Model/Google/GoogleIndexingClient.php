<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Google;

use <PERSON>uz<PERSON><PERSON>ttp\Client;
use Guz<PERSON><PERSON>ttp\Exception\RequestException;
use <PERSON><PERSON><PERSON>\Model\Configuration;
use Nette\Utils\Json;
use <PERSON>\Debugger;

class GoogleIndexingClient
{
    private const INDEXING_API_URL = 'https://indexing.googleapis.com/v3/urlNotifications:publish';
    private const SEARCH_CONSOLE_API_URL = 'https://searchconsole.googleapis.com/v1/urlInspection/index:inspect';
    
    // Rate limits
    private const DAILY_QUOTA_LIMIT = 200; // Google Indexing API daily limit
    private const REQUESTS_PER_MINUTE_LIMIT = 600; // Google Indexing API per minute limit

    /** @var string|null */
    private $serviceAccountKey;

    /** @var string|null */
    private $accessToken;

    /** @var \DateTime|null */
    private $tokenExpiresAt;

    public function __construct(Configuration $configuration)
    {
        $this->serviceAccountKey = $configuration->getGoogleServiceAccountKey();
    }

    /**
     * Pošle URL do Google Indexing API
     */
    public function requestIndexing(string $url, string $type = 'URL_UPDATED'): array
    {
        $accessToken = $this->getAccessToken();
        
        $client = new Client(['verify' => false]);
        
        $body = [
            'url' => $url,
            'type' => $type
        ];

        try {
            $response = $client->post(self::INDEXING_API_URL, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $accessToken,
                    'Content-Type' => 'application/json'
                ],
                'body' => Json::encode($body),
            ]);

            $statusCode = $response->getStatusCode();
            $responseData = $response->getBody()->getContents();

            return [
                'success' => $statusCode === 200,
                'status_code' => $statusCode,
                'response' => $responseData,
                'error' => null
            ];

        } catch (RequestException $e) {
            $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 0;
            $responseData = $e->getResponse() ? $e->getResponse()->getBody()->getContents() : null;

            // Zkrátíme chybovou zprávu pro databázi
            $errorMessage = $e->getMessage();
            if (strlen($errorMessage) > 250) {
                $errorMessage = substr($errorMessage, 0, 250) . '...';
            }

            return [
                'success' => false,
                'status_code' => $statusCode,
                'response' => $responseData,
                'error' => $errorMessage
            ];
        }
    }

    /**
     * Zkontroluje stav indexace URL přes Search Console API v1
     */
    public function checkIndexingStatus(string $url, string $siteUrl): array
    {
        $accessToken = $this->getAccessToken();

        $client = new Client(['verify' => false]);

        $body = [
            'inspectionUrl' => $url,
            'siteUrl' => $siteUrl,
            'languageCode' => 'cs-CZ'
        ];

        try {
            $response = $client->post(self::SEARCH_CONSOLE_API_URL, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $accessToken,
                    'Content-Type' => 'application/json'
                ],
                'body' => Json::encode($body),
            ]);

            $statusCode = $response->getStatusCode();
            $responseData = $response->getBody()->getContents();
            $data = Json::decode($responseData, Json::FORCE_ARRAY);

            $isIndexed = false;
            if (isset($data['inspectionResult']['indexStatusResult']['verdict'])) {
                $verdict = $data['inspectionResult']['indexStatusResult']['verdict'];
                $isIndexed = in_array($verdict, ['PASS', 'PARTIAL']);
            }

            return [
                'success' => $statusCode === 200,
                'status_code' => $statusCode,
                'response' => $responseData,
                'is_indexed' => $isIndexed,
                'error' => null
            ];

        } catch (RequestException $e) {
            $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 0;
            $responseData = $e->getResponse() ? $e->getResponse()->getBody()->getContents() : null;

            // Zkrátíme chybovou zprávu pro databázi
            $errorMessage = $e->getMessage();
            if (strlen($errorMessage) > 250) {
                $errorMessage = substr($errorMessage, 0, 250) . '...';
            }

            return [
                'success' => false,
                'status_code' => $statusCode,
                'response' => $responseData,
                'is_indexed' => false,
                'error' => $errorMessage
            ];
        }
    }

    /**
     * Získá access token pro Google API
     */
    private function getAccessToken(): string
    {
        if ($this->accessToken && $this->tokenExpiresAt && $this->tokenExpiresAt > new \DateTime()) {
            return $this->accessToken;
        }

        if (!$this->serviceAccountKey) {
            throw new \Exception('Google Service Account Key is not configured');
        }

        $serviceAccount = Json::decode($this->serviceAccountKey, Json::FORCE_ARRAY);
        
        $now = time();
        $expiry = $now + 3600; // 1 hour

        $header = [
            'alg' => 'RS256',
            'typ' => 'JWT'
        ];

        $payload = [
            'iss' => $serviceAccount['client_email'],
            'scope' => 'https://www.googleapis.com/auth/indexing https://www.googleapis.com/auth/webmasters',
            'aud' => 'https://oauth2.googleapis.com/token',
            'iat' => $now,
            'exp' => $expiry
        ];

        $jwt = $this->createJWT($header, $payload, $serviceAccount['private_key']);

        $client = new Client(['verify' => false]);
        
        try {
            $response = $client->post('https://oauth2.googleapis.com/token', [
                'form_params' => [
                    'grant_type' => 'urn:ietf:params:oauth:grant-type:jwt-bearer',
                    'assertion' => $jwt
                ]
            ]);

            $data = Json::decode($response->getBody()->getContents(), Json::FORCE_ARRAY);
            
            $this->accessToken = $data['access_token'];
            $this->tokenExpiresAt = new \DateTime('+' . ($data['expires_in'] - 60) . ' seconds');
            
            return $this->accessToken;

        } catch (RequestException $e) {
            throw new \Exception('Failed to get Google access token: ' . $e->getMessage());
        }
    }

    /**
     * Vytvoří JWT token
     */
    private function createJWT(array $header, array $payload, string $privateKey): string
    {
        $headerEncoded = $this->base64UrlEncode(Json::encode($header));
        $payloadEncoded = $this->base64UrlEncode(Json::encode($payload));
        
        $signature = '';
        openssl_sign($headerEncoded . '.' . $payloadEncoded, $signature, $privateKey, OPENSSL_ALGO_SHA256);
        $signatureEncoded = $this->base64UrlEncode($signature);
        
        return $headerEncoded . '.' . $payloadEncoded . '.' . $signatureEncoded;
    }

    /**
     * Base64 URL encode
     */
    private function base64UrlEncode(string $data): string
    {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }

    /**
     * Zkontroluje, zda je možné poslat další API request (rate limiting)
     */
    public function canMakeRequest(int $dailyRequestCount, int $minuteRequestCount): bool
    {
        return $dailyRequestCount < self::DAILY_QUOTA_LIMIT && 
               $minuteRequestCount < self::REQUESTS_PER_MINUTE_LIMIT;
    }

    /**
     * Vrátí denní limit requestů
     */
    public function getDailyLimit(): int
    {
        return self::DAILY_QUOTA_LIMIT;
    }

    /**
     * Vrátí minutový limit requestů
     */
    public function getMinuteLimit(): int
    {
        return self::REQUESTS_PER_MINUTE_LIMIT;
    }
}
