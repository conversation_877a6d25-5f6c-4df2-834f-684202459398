<?php

// <PERSON><PERSON><PERSON><PERSON>ý test bez Nette frameworku
*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

echo "=== Google API Test ===\n\n";

// Vytvoříme JWT token
function createJWT($serviceAccountKey) {
    $serviceAccount = json_decode($serviceAccountKey, true);

    $now = time();
    $expiry = $now + 3600;

    $header = json_encode(['alg' => 'RS256', 'typ' => 'JWT']);
    $payload = json_encode([
        'iss' => $serviceAccount['client_email'],
        'scope' => 'https://www.googleapis.com/auth/indexing',
        'aud' => 'https://oauth2.googleapis.com/token',
        'iat' => $now,
        'exp' => $expiry
    ]);

    $base64Header = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
    $base64Payload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));

    $signature = '';
    openssl_sign($base64Header . '.' . $base64Payload, $signature, $serviceAccount['private_key'], 'SHA256');
    $base64Signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));

    return $base64Header . '.' . $base64Payload . '.' . $base64Signature;
}

// Získáme access token
try {
    $jwt = createJWT($serviceAccountKey);

    $tokenData = http_build_query([
        'grant_type' => 'urn:ietf:params:oauth:grant-type:jwt-bearer',
        'assertion' => $jwt
    ]);

    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => 'Content-Type: application/x-www-form-urlencoded',
            'content' => $tokenData
        ]
    ]);

    $response = file_get_contents('https://oauth2.googleapis.com/token', false, $context);
    $tokenResponse = json_decode($response, true);

    if (isset($tokenResponse['access_token'])) {
        $accessToken = $tokenResponse['access_token'];
        echo "✅ Access token získán: " . substr($accessToken, 0, 50) . "...\n\n";
    } else {
        echo "❌ Chyba při získávání access token: " . $response . "\n";
        exit(1);
    }
} catch (Exception $e) {
    echo "❌ Chyba při získávání access token: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 1: Google Indexing API
echo "=== Test 1: Google Indexing API ===\n";
$indexingUrl = 'https://indexing.googleapis.com/v3/urlNotifications:publish';
$indexingData = json_encode([
    'url' => 'https://www.kaufino.com/',
    'type' => 'URL_UPDATED'
]);

$indexingCommand = sprintf(
    'curl -s -X POST "%s" -H "Authorization: Bearer %s" -H "Content-Type: application/json" -d \'%s\'',
    $indexingUrl,
    $accessToken,
    $indexingData
);

echo "Request URL: $indexingUrl\n";
echo "Request Data: $indexingData\n\n";

$indexingResult = shell_exec($indexingCommand);
echo "Response: $indexingResult\n\n";

// Test 2: Google Search Console API
echo "=== Test 2: Google Search Console API ===\n";
$searchConsoleUrl = 'https://searchconsole.googleapis.com/v1/urlInspection/index:inspect';
$searchConsoleData = json_encode([
    'inspectionUrl' => 'https://www.kaufino.com/',
    'siteUrl' => 'sc-domain:kaufino.com',
    'languageCode' => 'cs-CZ'
]);

$searchConsoleCommand = sprintf(
    'curl -s -X POST "%s" -H "Authorization: Bearer %s" -H "Content-Type: application/json" -d \'%s\'',
    $searchConsoleUrl,
    $accessToken,
    $searchConsoleData
);

echo "Request URL: $searchConsoleUrl\n";
echo "Request Data: $searchConsoleData\n\n";

$searchConsoleResult = shell_exec($searchConsoleCommand);
echo "Response: $searchConsoleResult\n\n";

// Test 3: Zkusíme jiný formát pro Search Console
echo "=== Test 3: Search Console s URL prefix ===\n";
$searchConsoleData2 = json_encode([
    'inspectionUrl' => 'https://www.kaufino.com/',
    'siteUrl' => 'https://www.kaufino.com/',
    'languageCode' => 'cs-CZ'
]);

$searchConsoleCommand2 = sprintf(
    'curl -s -X POST "%s" -H "Authorization: Bearer %s" -H "Content-Type: application/json" -d \'%s\'',
    $searchConsoleUrl,
    $accessToken,
    $searchConsoleData2
);

echo "Request Data: $searchConsoleData2\n\n";

$searchConsoleResult2 = shell_exec($searchConsoleCommand2);
echo "Response: $searchConsoleResult2\n\n";

echo "=== Test dokončen ===\n";
