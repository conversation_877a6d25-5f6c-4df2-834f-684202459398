<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Seo;

use <PERSON><PERSON><PERSON>\Model\EntityManager;
use <PERSON><PERSON><PERSON>\Model\Google\GoogleIndexingClient;
use <PERSON><PERSON><PERSON>\Model\Seo\Entities\GoogleIndex;
use <PERSON><PERSON><PERSON>\Model\Seo\Entities\GoogleIndexProcess;
use <PERSON><PERSON><PERSON>\Model\Seo\Repositories\GoogleIndexRepository;
use Ka<PERSON>ino\Model\Seo\Repositories\GoogleIndexProcessRepository;
use <PERSON><PERSON><PERSON>\Model\Websites\Entities\Website;
use Nette\Utils\Json;
use Tracy\Debugger;

class GoogleIndexFacade
{
    /** @var EntityManager */
    private $entityManager;

    /** @var GoogleIndexRepository */
    private $googleIndexRepository;

    /** @var GoogleIndexProcessRepository */
    private $googleIndexProcessRepository;

    /** @var GoogleIndexingClient */
    private $googleIndexingClient;

    public function __construct(
        EntityManager $entityManager,
        GoogleIndexingClient $googleIndexingClient
    ) {
        $this->entityManager = $entityManager;
        $this->googleIndexingClient = $googleIndexingClient;
        $this->googleIndexRepository = $entityManager->getRepository(GoogleIndex::class);
        $this->googleIndexProcessRepository = $entityManager->getRepository(GoogleIndexProcess::class);
    }

    public function addUrlToIndex(Website $website, string $url): GoogleIndex
    {
        $googleIndex = $this->googleIndexRepository->findByUrl($url);
        
        if (!$googleIndex) {
            $googleIndex = new GoogleIndex($website, $url);
            $this->entityManager->persist($googleIndex);
            $this->entityManager->flush();
        }
        
        return $googleIndex;
    }

    public function processIndexingRequest(GoogleIndex $googleIndex): bool
    {
        $dailyCount = $this->googleIndexProcessRepository->countApiRequestsToday();
        $minuteCount = $this->googleIndexProcessRepository->countApiRequestsThisMinute();
        
        if (!$this->googleIndexingClient->canMakeRequest($dailyCount, $minuteCount)) {
            Debugger::log("Rate limit reached. Daily: {$dailyCount}, Minute: {$minuteCount}", 'google-indexing');
            return false;
        }

        $process = new GoogleIndexProcess($googleIndex, GoogleIndexProcess::API_TYPE_INDEXING, GoogleIndexProcess::REQUEST_TYPE_URL_UPDATED);
        $process->setRequestData(Json::encode(['url' => $googleIndex->getUrl()]));

        $result = $this->googleIndexingClient->requestIndexing($googleIndex->getUrl());

        if ($result['success']) {
            $process->markSuccess($result['status_code'], $result['response']);
            $googleIndex->markIndexingRequested();
            $googleIndex->setLastIndexingStatus(GoogleIndex::STATUS_REQUESTED);
            
            Debugger::log("Indexing request sent for: " . $googleIndex->getUrl(), 'google-indexing');
        } else {
            $process->markError($result['status_code'], $result['error'] ?? 'Unknown error', $result['response']);
            $googleIndex->setLastErrorMessage($result['error'] ?? 'Unknown error');
            
            Debugger::log("Indexing request failed for: " . $googleIndex->getUrl() . " - " . $result['error'], 'google-indexing');
        }
        
        $this->entityManager->persist($process);
        $this->entityManager->persist($googleIndex);
        $this->entityManager->flush();
        
        return $result['success'];
    }

    public function checkIndexingStatus(GoogleIndex $googleIndex): bool
    {
        $dailyCount = $this->googleIndexProcessRepository->countApiRequestsToday();
        $minuteCount = $this->googleIndexProcessRepository->countApiRequestsThisMinute();
        
        if (!$this->googleIndexingClient->canMakeRequest($dailyCount, $minuteCount)) {
            Debugger::log("Rate limit reached for status check. Daily: {$dailyCount}, Minute: {$minuteCount}", 'google-indexing');
            return false;
        }

        $process = new GoogleIndexProcess($googleIndex, GoogleIndexProcess::API_TYPE_SEARCH_CONSOLE, GoogleIndexProcess::REQUEST_TYPE_STATUS_CHECK);
        $siteUrl = $this->getSiteUrlForSearchConsole($googleIndex->getWebsite()->getDomain());

        $process->setRequestData(Json::encode([
            'url' => $googleIndex->getUrl(),
            'siteUrl' => $siteUrl
        ]));

        $result = $this->googleIndexingClient->checkIndexingStatus($googleIndex->getUrl(), $siteUrl);

        if ($result['success']) {
            $process->markSuccess($result['status_code'], $result['response']);
            $googleIndex->setLastStatusCheckAt(new \DateTime());
            
            if ($result['is_indexed']) {
                $googleIndex->setIndexed(true);
                $googleIndex->setLastIndexingStatus('indexed');
                Debugger::log("URL is indexed: " . $googleIndex->getUrl(), 'google-indexing');
            } else {
                $googleIndex->setLastIndexingStatus('not_indexed');
                Debugger::log("URL is not indexed yet: " . $googleIndex->getUrl(), 'google-indexing');
            }
        } else {
            $process->markError($result['status_code'], $result['error'] ?? 'Unknown error', $result['response']);
            $googleIndex->setLastErrorMessage($result['error'] ?? 'Unknown error');
            
            Debugger::log("Status check failed for: " . $googleIndex->getUrl() . " - " . $result['error'], 'google-indexing');
        }

        $this->entityManager->persist($process);
        $this->entityManager->persist($googleIndex);
        $this->entityManager->flush();
        
        return $result['success'];
    }

    public function findUrlsToIndex(int $limit = 50): array
    {
        return $this->googleIndexRepository->findUrlsToIndex($limit);
    }

    public function findUrlsToCheckStatus(int $limit = 50): array
    {
        return $this->googleIndexRepository->findUrlsToCheckStatus($limit);
    }

    private function getSiteUrlForSearchConsole(string $domain): string
    {
        $parsedUrl = parse_url($domain);

        if (!$parsedUrl) {
            return $domain;
        }

        $host = $parsedUrl['host'] ?? '';

        $cleanHost = str_replace('www.', '', $host);
        return 'sc-domain:' . $cleanHost;
    }

    public function getStatistics(): array
    {
        return [
            'pending_indexing' => $this->googleIndexRepository->countPendingIndexing(),
            'indexed' => $this->googleIndexRepository->countIndexed(),
            'api_requests_today' => $this->googleIndexProcessRepository->countApiRequestsToday(),
            'api_requests_this_minute' => $this->googleIndexProcessRepository->countApiRequestsThisMinute(),
            'daily_limit' => $this->googleIndexingClient->getDailyLimit(),
            'minute_limit' => $this->googleIndexingClient->getMinuteLimit()
        ];
    }

    public function findByUrl(string $url): ?GoogleIndex
    {
        return $this->googleIndexRepository->findByUrl($url);
    }

    public function save(GoogleIndex $googleIndex): void
    {
        $this->entityManager->persist($googleIndex);
        $this->entityManager->flush();
    }

    public function getRecentLogs(int $limit = 100): array
    {
        return $this->googleIndexProcessRepository->findRecentLogs($limit);
    }

    public function getErrorLogs(int $limit = 50): array
    {
        return $this->googleIndexProcessRepository->findErrorLogs($limit);
    }
}
