<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Seo\Repositories;

use Doctrine\ORM\EntityRepository;
use <PERSON><PERSON><PERSON>\Model\Seo\Entities\GoogleIndex;
use <PERSON><PERSON><PERSON>\Model\Seo\Entities\GoogleIndexProcess;

class GoogleIndexProcessRepository extends EntityRepository
{
    public function findByGoogleIndex(GoogleIndex $googleIndex, int $limit = 50): array
    {
        return $this->createQueryBuilder('gip')
            ->where('gip.googleIndex = :googleIndex')
            ->setParameter('googleIndex', $googleIndex)
            ->setMaxResults($limit)
            ->orderBy('gip.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    public function findRecentLogs(int $limit = 100): array
    {
        return $this->createQueryBuilder('gip')
            ->setMaxResults($limit)
            ->orderBy('gip.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    public function findErrorLogs(int $limit = 50): array
    {
        return $this->createQueryBuilder('gip')
            ->where('gip.success = 0')
            ->setMaxResults($limit)
            ->orderBy('gip.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    public function countApiRequestsToday(): int
    {
        return (int) $this->createQueryBuilder('gip')
            ->select('COUNT(gip.id)')
            ->where('gip.createdAt >= :today')
            ->setParameter('today', new \DateTime('today'))
            ->getQuery()
            ->getSingleScalarResult();
    }

    public function countApiRequestsThisMinute(): int
    {
        return (int) $this->createQueryBuilder('gip')
            ->select('COUNT(gip.id)')
            ->where('gip.createdAt >= :oneMinuteAgo')
            ->setParameter('oneMinuteAgo', new \DateTime('-1 minute'))
            ->getQuery()
            ->getSingleScalarResult();
    }

    public function findSuccessfulRequests(int $limit = 100): array
    {
        return $this->createQueryBuilder('gip')
            ->where('gip.success = 1')
            ->setMaxResults($limit)
            ->orderBy('gip.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    public function findByApiType(string $apiType, int $limit = 100): array
    {
        return $this->createQueryBuilder('gip')
            ->where('gip.apiType = :apiType')
            ->setParameter('apiType', $apiType)
            ->setMaxResults($limit)
            ->orderBy('gip.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    public function findIndexingRequests(int $limit = 100): array
    {
        return $this->findByApiType(GoogleIndexProcess::API_TYPE_INDEXING, $limit);
    }

    public function findSearchConsoleRequests(int $limit = 100): array
    {
        return $this->findByApiType(GoogleIndexProcess::API_TYPE_SEARCH_CONSOLE, $limit);
    }
}
