<?php

declare(strict_types=1);

namespace Ka<PERSON>ino\Model\Seo\Entities;

use DateTime;
use Doctrine\ORM\Mapping as ORM;
use Ka<PERSON>ino\Model\Websites\Entities\Website;

/**
 * @ORM\Entity(repositoryClass="<PERSON><PERSON><PERSON>\Model\Seo\Repositories\GoogleIndexRepository")
 * @ORM\Table(name="kaufino_seo_google_index", uniqueConstraints={
 *     @ORM\UniqueConstraint(name="google_index_url_unique", columns={"url"})}
 * )
 */
class GoogleIndex
{
    // Indexing Status Constants
    public const STATUS_REQUESTED = 'requested';
    public const STATUS_INDEXED = 'indexed';
    public const STATUS_NOT_INDEXED = 'not_indexed';
    /**
     * @var int
     * @ORM\Column(type="integer", nullable=FALSE)
     * @ORM\Id
     * @ORM\GeneratedValue
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity="\Kaufino\Model\Websites\Entities\Website")
     * @ORM\JoinColumn(name="website_id", referencedColumnName="id")
     */
    protected $website;

    /**
     * @ORM\Column(type="string", length=2048, nullable=false)
     */
    protected $url;

    /**
     * @ORM\Column(type="boolean")
     */
    protected $isIndexed = false;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     */
    private $lastIndexingRequestAt;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     */
    private $lastStatusCheckAt;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     */
    private $indexedAt;

    /**
     * @ORM\Column(type="string", nullable=true)
     */
    protected $lastIndexingStatus;

    /**
     * @ORM\Column(type="text", nullable=true)
     */
    protected $lastErrorMessage;

    /**
     * @ORM\Column(type="integer")
     */
    protected $requestCount = 0;

    /**
     * @ORM\Column(type="datetime")
     */
    private $createdAt;

    /**
     * @ORM\Column(type="datetime")
     */
    private $updatedAt;

    public function __construct(Website $website, string $url)
    {
        $this->website = $website;
        $this->url = $url;
        $this->createdAt = new DateTime();
        $this->updatedAt = new DateTime();
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getWebsite(): Website
    {
        return $this->website;
    }

    public function getUrl(): string
    {
        return $this->url;
    }

    public function isIndexed(): bool
    {
        return $this->isIndexed;
    }

    public function setIndexed(bool $isIndexed): void
    {
        $this->isIndexed = $isIndexed;
        if ($isIndexed) {
            $this->indexedAt = new DateTime();
        }
        $this->updatedAt = new DateTime();
    }

    public function getLastIndexingRequestAt(): ?DateTime
    {
        return $this->lastIndexingRequestAt;
    }

    public function setLastIndexingRequestAt(?DateTime $lastIndexingRequestAt): void
    {
        $this->lastIndexingRequestAt = $lastIndexingRequestAt;
        $this->updatedAt = new DateTime();
    }

    public function getLastStatusCheckAt(): ?DateTime
    {
        return $this->lastStatusCheckAt;
    }

    public function setLastStatusCheckAt(?DateTime $lastStatusCheckAt): void
    {
        $this->lastStatusCheckAt = $lastStatusCheckAt;
        $this->updatedAt = new DateTime();
    }

    public function getIndexedAt(): ?DateTime
    {
        return $this->indexedAt;
    }

    public function getLastIndexingStatus(): ?string
    {
        return $this->lastIndexingStatus;
    }

    public function setLastIndexingStatus(?string $lastIndexingStatus): void
    {
        $this->lastIndexingStatus = $lastIndexingStatus;
        $this->updatedAt = new DateTime();
    }

    public function getLastErrorMessage(): ?string
    {
        return $this->lastErrorMessage;
    }

    public function setLastErrorMessage(?string $lastErrorMessage): void
    {
        $this->lastErrorMessage = $lastErrorMessage;
        $this->updatedAt = new DateTime();
    }

    public function getRequestCount(): int
    {
        return $this->requestCount;
    }

    public function incrementRequestCount(): void
    {
        $this->requestCount++;
        $this->updatedAt = new DateTime();
    }

    public function getCreatedAt(): DateTime
    {
        return $this->createdAt;
    }

    public function getUpdatedAt(): DateTime
    {
        return $this->updatedAt;
    }

    public function markIndexingRequested(): void
    {
        $this->lastIndexingRequestAt = new DateTime();
        $this->incrementRequestCount();
    }

    public function needsStatusCheck(): bool
    {
        // Kontroluj stav každých 6 hodin, pokud ještě není indexováno
        if ($this->isIndexed) {
            return false;
        }

        if (!$this->lastStatusCheckAt) {
            return true;
        }

        $sixHoursAgo = new DateTime('-6 hours');
        return $this->lastStatusCheckAt < $sixHoursAgo;
    }

    public function needsIndexingRequest(): bool
    {
        // Pošli request, pokud ještě nebyl poslán nebo pokud je to více než 24 hodin
        if ($this->isIndexed) {
            return false;
        }

        if (!$this->lastIndexingRequestAt) {
            return true;
        }

        $oneDayAgo = new DateTime('-24 hours');
        return $this->lastIndexingRequestAt < $oneDayAgo;
    }
}