<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Leaflets\Repositories;

use <PERSON><PERSON>ino\Model\EntityRepository;
use <PERSON><PERSON>ino\Model\Geo\Entities\City;
use <PERSON><PERSON>ino\Model\Leaflets\Entities\Leaflet;
use <PERSON><PERSON>ino\Model\Localization\Entities\Localization;
use Ka<PERSON>ino\Model\Shops\Entities\Shop;
use Kaufino\Model\Tags\Entities\Tag;
use Kaufino\Model\Websites\Entities\Website;
use Tracy\Debugger;

class LeafletRepository extends EntityRepository
{
	public function getLeaflets(Localization $localization = null, $current = true, $includingDeleted = false, $onlyChecked = true)
	{
		$qb = $this->createQueryBuilder('l');

		if ($localization) {
			$qb->andWhere('l.localization = :localization')
				->setParameter('localization', $localization);
		}

		if ($current) {
			$qb->andWhere('l.validTill > :now')
				->setParameter('now', new \DateTime());
		}

		if (!$includingDeleted) {
			$qb->andWhere('(l.deletedAt IS NOT NULL AND l.deletedAt > :now) OR l.deletedAt IS NULL')
				->setParameter('now', new \DateTime());
		}

		if ($onlyChecked) {
			$qb->andWhere('l.checked = 1');
		}

		return $qb;
	}

	public function findLeafletBySlug(Localization $localization, string $slug, bool $onlyValid): ?Leaflet
	{
		$qb = $this->getLeaflets($localization, $onlyValid, true, false)
			->andWhere('l.slug = :slug')
			->setParameter('slug', $slug);

		return $qb->getQuery()->getOneOrNullResult();
	}

	public function getLeafletsByShop(Shop $shop, $current, bool $withFirstPage = false, ?Leaflet $exceptLeaflet = null, ?bool $onlyExpired = null, $onlyChecked = true)
	{
		$qb = $this->getLeaflets(null, $current, false, $onlyChecked)
			->andWhere('l.shop = :shop')
			->setParameter('shop', $shop)
			->addOrderBy('l.priority', 'DESC')
			->addOrderBy('l.validTill', 'DESC')
            ->andWhere('l.archivedAt IS NULL')
		;

		if ($onlyExpired) {
			$qb->andWhere('l.validTill <= :now')
				->setParameter('now', new \DateTime());
		}

		if ($withFirstPage === true) {
			$qb->innerJoin('l.pages', 'p')
				->andWhere('p.pageNumber IN (1, 2)')
				->addSelect('p')
            ;
		}

		if ($exceptLeaflet) {
			$qb->andWhere('l != :leaflet')
				->setParameter('leaflet', $exceptLeaflet);
		}

		return $qb->getQuery();
	}

    public function getLeafletsByShops(array $shops, $current = true, ?array $exceptLeaflets = null, ?string $websiteType = null)
    {
        $qb = $this->getLeaflets(null, $current)
            ->innerJoin('l.shop', 's')
            ->innerJoin('l.pages', 'p')
            ->andWhere('s IN (:shops)')
            ->setParameter('shops', $shops)
            ->addOrderBy('l.top', 'DESC')
            ->addOrderBy('l.primary', 'DESC')
            ->addOrderBy('s.priorityLeaflets', 'DESC')
            ->andWhere('p.pageNumber = 1')
            ->addSelect('p');

        if ($websiteType === Website::MODULE_OFERTO) {
            $qb->andWhere('s.activeOferto = 1');
        } elseif ($websiteType === Website::MODULE_KAUFINO || $websiteType === Website::MODULE_KAUFINO_SUBDOMAIN) {
            $qb->andWhere('s.activeKaufino = 1');
        } elseif ($websiteType === Website::MODULE_LETADO) {
            $qb->andWhere('s.activeLetado = 1');
        }

        if ($exceptLeaflets) {
            $qb->andWhere('l NOT IN (:exceptLeaflets)')
                ->setParameter('exceptLeaflets', $exceptLeaflets);
        }

        if ($websiteType === Website::MODULE_KAUFINO) {
            $qb->andWhere('s.hidden = 0');
        }

        return $qb;
    }

	public function getLeafletsByTag(Tag $tag, $current, $onlyStores, ?string $websiteType = null)
	{
		$qb = $this->getLeaflets(null, $current)
			->innerJoin('l.shop', 's')
			->andWhere('s.tag = :tag')
            ->addOrderBy('l.top', 'DESC')
            ->addOrderBy('l.primary', 'DESC')
            ->addOrderBy('s.priorityLeaflets', 'DESC')
			->setParameter('tag', $tag);

        if ($websiteType === Website::MODULE_OFERTO) {
            $qb->andWhere('s.activeOferto = 1');
        } elseif ($websiteType === Website::MODULE_KAUFINO || $websiteType === Website::MODULE_KAUFINO_SUBDOMAIN) {
            $qb->andWhere('s.activeKaufino = 1');
        } elseif ($websiteType === Website::MODULE_LETADO) {
            $qb->andWhere('s.activeLetado = 1');
        }

        if ($websiteType === Website::MODULE_KAUFINO) {
            $qb->andWhere('s.hidden = 0');
        }

		if ($onlyStores) {
			$qb->andWhere('s.type = :type')
				->setParameter('type', Shop::TYPE_STORE);
		}

		return $qb->getQuery();
	}

    public function findArchivedLeaflets(Shop $shop)
    {
        return $this->getLeaflets(null, false)
            ->innerJoin('l.shop', 's')
            ->andWhere('s = :shop')
            ->setParameter('shop', $shop)
            ->andWhere('l.validTill <= :now')
            ->andWhere('l.archiveAt >= :now')
            ->setParameter('now', new \DateTime())
            ->addOrderBy('l.archiveAt', 'ASC')
            ->getQuery()
            ->getResult()
        ;
    }

	public function findLeafletsByCity(City $city, ?Shop $shop = null, ?int $limit = 20, bool $withoutAllWideLeaflets = false)
	{
		$qb = $this->getLeaflets($city->getLocalization())
			->innerJoin('l.shop', 's')
            ->innerJoin('l.cities', 'c')
			->andWhere('s.activeLeaflets = true')
            ->andWhere('c.id = :cityId')
            ->setParameter('cityId', $city->getId())
            ->addOrderBy('s.priorityLeaflets', 'DESC')
            ->addOrderBy('l.top', 'DESC')
            ->addOrderBy('l.primary', 'DESC')
			->setMaxResults($limit);

		if ($shop) {
			$qb->andWhere('l.shop = :shop')
				->setParameter('shop', $shop);
		} else {
			$shopsByCity = $city->getShops();

			$qb->andWhere('l.shop IN (:shopsByCity)')
				->setParameter('shopsByCity', $shopsByCity->toArray());
		}

		$limitedLeaflets = $qb->getQuery()->getResult();

		if ($withoutAllWideLeaflets === true) {
			return $limitedLeaflets;
		}

		$qb = $this->getLeaflets($city->getLocalization())
			->leftJoin('l.cities', 'c')
			->innerJoin('l.shop', 's')
			->andWhere('s.activeLeaflets = true')
			->andWhere('c.id IS NULL')
            ->addOrderBy('l.top', 'DESC')
            ->addOrderBy('l.primary', 'DESC')
            ->addOrderBy('s.priorityLeaflets', 'DESC')
			->setMaxResults($limit);

		if ($shop) {
			$qb->andWhere('l.shop = :shop')
				->setParameter('shop', $shop);
		}

		$allWideLeaflets = $qb->getQuery()->getResult();

		$leaflets = [];
		foreach ($limitedLeaflets as $leaflet) {
            $leaflets[] = $leaflet;
		}

		foreach ($allWideLeaflets as $leaflet) {
            $leaflets[] = $leaflet;
		}

		return $leaflets;
	}

    public function findIn(array $ids)
    {
        $qb = $this->createQueryBuilder('l')
            ->innerJoin('l.shop', 's')
            ->andWhere('l.id IN (:ids)')
            ->setParameter('ids', $ids)
            ->addOrderBy('s.priorityLeaflets', 'DESC')
            ->addOrderBy('l.top', 'DESC')
            ->addOrderBy('l.primary', 'DESC')
        ;

        return $qb->getQuery()->getResult();
    }

	public function findLeafletsToArchive(int $limit = 1000)
	{
		$qb = $this->createQueryBuilder('l')
			->andWhere('l.archiveAt IS NOT NULL')
			->andWhere('l.archiveAt <= :now')
			->andWhere('l.archivedAt IS NULL')
			->setParameter('now', new \DateTime())
			->setMaxResults($limit)
		;

		return $qb->getQuery()->getResult();
	}

	public function findLeafletsFromOffers(array $offers)
	{
		return $this->getLeaflets()
			->innerJoin('l.pages', 'p')
			->innerJoin('p.offers', 'o')
			->andWhere('o IN (:offers)')
			->setParameter('offers', $offers)
			->addGroupBy('l.id')
			->getQuery()
			->getResult();
	}

    public function findTopLeafletsByShops(array $shops, ?int $limit = null)
    {
        $qb = $this->getLeaflets()
            ->innerJoin('l.shop', 's')
            ->andWhere('s IN (:shops)')
            ->setParameter('shops', $shops)
            ->andWhere('l.top = true')
            ->addOrderBy('s.priorityLeaflets', 'DESC')
        ;

        if ($limit) {
            $qb->setMaxResults($limit);
        }

        return $qb->getQuery()->getResult();
    }

    public function getRelatedLeaflets(Leaflet $leaflet)
    {
        return $this->getLeaflets($leaflet->getLocalization())
            ->leftJoin('l.shop', 's')
            ->andWhere('l != :leaflet')
            ->setParameter('leaflet', $leaflet)
            ->setParameter('shop', $leaflet->getShop())
            ->addOrderBy('l.isOfferistaPromo', 'DESC')
            ->addOrderBy('l.offeristaBrochureId', 'DESC')
            ->addOrderBy('CASE WHEN s = :shop THEN 0 ELSE 1 END', 'ASC')
            ->addOrderBy('l.priority', 'DESC')
            ->addOrderBy('l.validTill', 'DESC');
    }

    public function findTopLeafletsByShop(Shop $shop, int $limit = 3)
    {
        return $this->getLeaflets($shop->getLocalization())
            ->leftJoin('l.shop', 's')
            ->addOrderBy('l.isOfferistaPromo', 'DESC')
            ->addOrderBy('l.offeristaBrochureId', 'DESC')
            ->addOrderBy('CASE WHEN s = :shop THEN 0 ELSE 1 END', 'ASC')
            ->addOrderBy('l.priority', 'DESC')
            ->addOrderBy('l.validTill', 'DESC')
            ->setParameter('shop', $shop)
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult()
        ;
    }

    public function findRelatedLeaflet(Leaflet $leaflet)
    {
        return $this->getRelatedLeaflets($leaflet)
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
    }

    public function findRelatedLeaflets(Leaflet $leaflet, int $limit)
    {
        return $this->getRelatedLeaflets($leaflet)
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    public function findPriorityLeaflets(Localization $localization, int $limit, string $websiteType)
    {
        $qb = $this->getLeaflets($localization)
            ->innerJoin('l.shop', 's')
            ->andWhere('l.archivedAt IS NULL')
            ->andWhere('l.validTill > :now')
            ->setParameter('now', new \DateTime())
            ->addOrderBy('l.createdAt', 'DESC')
            ->setMaxResults($limit)
        ;

        if ($websiteType === Website::MODULE_OFERTO) {
            $qb->andWhere('s.activeOferto = 1');
        } elseif ($websiteType === Website::MODULE_KAUFINO || $websiteType === Website::MODULE_KAUFINO_SUBDOMAIN) {
            $qb->andWhere('s.activeKaufino = 1');
        } elseif ($websiteType === Website::MODULE_LETADO) {
            $qb->andWhere('s.activeLetado = 1');
        }

        if ($websiteType === Website::MODULE_KAUFINO) {
            $qb->andWhere('s.hidden = 0');
        }

        return $qb->getQuery()->getResult();
    }

    public function findLeafletsToProcessGoogleIndex()
    {
        return $this->getLeaflets()
            ->innerJoin('l.shop', 's')
            ->andWhere('l.primary = true')
            ->andWhere('l.localization = 1')
            ->andWhere('l.validTill > :now')
            ->andWhere('l.archivedAt IS NULL')
            ->andWhere('l.deletedAt IS NULL')
            ->setParameter('now', new \DateTime())
            ->getQuery()
            ->getResult();
    }
}
