# Google Indexing - Konstanty

## P<PERSON>ehled konstant

Všechny stringy pro API typy, request typy a statusy byly převedeny na konstanty pro lepší udržovatelnost kódu.

## GoogleIndexProcess konstanty

### API Types
```php
GoogleIndexProcess::API_TYPE_INDEXING = 'indexing'
GoogleIndexProcess::API_TYPE_SEARCH_CONSOLE = 'search_console'
```

### Request Types
```php
GoogleIndexProcess::REQUEST_TYPE_URL_UPDATED = 'URL_UPDATED'
GoogleIndexProcess::REQUEST_TYPE_URL_DELETED = 'URL_DELETED'
GoogleIndexProcess::REQUEST_TYPE_STATUS_CHECK = 'status_check'
```

## GoogleIndex konstanty

### Indexing Status
```php
GoogleIndex::STATUS_REQUESTED = 'requested'
GoogleIndex::STATUS_INDEXED = 'indexed'
GoogleIndex::STATUS_NOT_INDEXED = 'not_indexed'
```

## Použití v kódu

### GoogleIndexFacade
```php
// Vytvoření procesu pro indexování
$process = new GoogleIndexProcess(
    $googleIndex, 
    GoogleIndexProcess::API_TYPE_INDEXING, 
    GoogleIndexProcess::REQUEST_TYPE_URL_UPDATED
);

// Vytvoření procesu pro kontrolu stavu
$process = new GoogleIndexProcess(
    $googleIndex, 
    GoogleIndexProcess::API_TYPE_SEARCH_CONSOLE, 
    GoogleIndexProcess::REQUEST_TYPE_STATUS_CHECK
);

// Nastavení statusu
$googleIndex->setLastIndexingStatus(GoogleIndex::STATUS_REQUESTED);
$googleIndex->setLastIndexingStatus(GoogleIndex::STATUS_INDEXED);
$googleIndex->setLastIndexingStatus(GoogleIndex::STATUS_NOT_INDEXED);
```

### GoogleIndexingClient
```php
// Defaultní request type pro indexování
public function requestIndexing(string $url, string $type = GoogleIndexProcess::REQUEST_TYPE_URL_UPDATED): array
```

### GoogleIndexProcessRepository
```php
// Nové metody s konstantami
$repository->findIndexingRequests(100);        // API_TYPE_INDEXING
$repository->findSearchConsoleRequests(100);   // API_TYPE_SEARCH_CONSOLE
$repository->findByApiType(GoogleIndexProcess::API_TYPE_INDEXING, 50);
```

## Výhody použití konstant

1. **Type safety** - IDE kontroluje existenci konstant
2. **Refactoring** - snadné přejmenování napříč celým kódem
3. **Dokumentace** - konstanty slouží jako dokumentace možných hodnot
4. **Chyby** - snížení rizika překlepů v stringech
5. **Udržovatelnost** - centralizované definice hodnot

## Migrace

Všechny hardcoded stringy byly nahrazeny konstantami:
- ✅ `'indexing'` → `GoogleIndexProcess::API_TYPE_INDEXING`
- ✅ `'search_console'` → `GoogleIndexProcess::API_TYPE_SEARCH_CONSOLE`
- ✅ `'URL_UPDATED'` → `GoogleIndexProcess::REQUEST_TYPE_URL_UPDATED`
- ✅ `'status_check'` → `GoogleIndexProcess::REQUEST_TYPE_STATUS_CHECK`
- ✅ `'requested'` → `GoogleIndex::STATUS_REQUESTED`
- ✅ `'indexed'` → `GoogleIndex::STATUS_INDEXED`
- ✅ `'not_indexed'` → `GoogleIndex::STATUS_NOT_INDEXED`
